import * as path from 'path';
import { expect, Locator, Page } from '@playwright/test';
import { BasePage } from '../base-page';
import { CalendarHelper } from '../../../../shared/calendar/calendar-helper';

export class RegularEnrollmentPage extends BasePage {
  // Date Picker locator
  readonly dateInputLocator: Locator = this.page.locator('div.ant-picker-input');
  private readonly selectDateOnCalendarLocator: string = 'css=td[title="${date}"] >> nth=0';
  readonly submitRequestDocumentButtonLocator: Locator = this.page.getByRole('button', { name: 'ยืนยัน' });
  readonly loadingSkeleton: Locator = this.page
    .locator('td > .ant-skeleton > .ant-skeleton-content > .ant-skeleton-title')
    .first();
  readonly loadingSpinLocator: Locator = this.page.locator('data-testid=pin-spinning');
  readonly inputEnrollmentFullNameLocator: Locator = this.page.locator('#enrollmentFullName');
  readonly inputPreEnrollmentFullNameLocator: Locator = this.page.locator('#preEnrollmentFullName');
  readonly inputFullNameLocator: Locator = this.page.locator('#fullName');
  readonly searchButtonLocator: Locator = this.page.locator('svg[data-icon="search"]');
  readonly viewDetailButtonLocator: Locator = this.page.getByRole('button', { name: 'ดูรายละเอียด' });
  readonly enrollmentLearningTabLocator: Locator = this.page.getByRole('tab', { name: 'กำลังเรียน' });
  readonly learningTabLocator: Locator = this.page.getByRole('tab', { name: 'ประวัติระหว่างเรียน' });
  readonly attentionCheckHistoryLocator: Locator = this.page.getByRole('cell', { name: 'ความสนใจ' });
  readonly rpcHistoryLocator: Locator = this.page.getByRole('cell', { name: 'ยืนยันตัวตน' });
  readonly spinningAnimationSelector: Locator = this.page.locator('css=span.ant-spin-dot');
  readonly labelSearchResultLocator: Locator = this.page.getByText('ผลการค้นหา: 1 รายการ');
  readonly filterStatusLocator: Locator = this.page.locator('xpath=//*[contains(text(), "สถานะ")]').first();
  readonly enrollmentStatusAllLocator: Locator = this.page.locator('li.ant-dropdown-menu-item-only-child');
  readonly filterStatusItemAllLocator: Locator = this.page.getByRole('menuitem');
  readonly nameInputLocator: Locator = this.page.getByPlaceholder('ชื่อจริง/นามสกุล');
  readonly requestDocumentButtonLocator: Locator = this.page.getByRole('button', { name: 'ขอเอกสาร' });
  readonly checkBoxCitizenCardLocator: Locator = this.page.locator(
    'div:nth-child(4) > .ant-tree-checkbox > .ant-tree-checkbox-inner',
  );
  readonly reasonRequestDocumentInputLocator: Locator = this.page.getByPlaceholder('กรอกเหตุผลการขอเอกสาร');
  readonly verifyFullNameLocator: Locator = this.page.locator(
    'div.ant-card-body >> div.ant-space-item >> h5.ant-typography',
  );
  readonly verifyCourseVersionLocator: Locator = this.page.locator(
    'span.ant-typography.ant-typography-secondary >> nth=0',
  );
  readonly bulkEnrollButtonLocator: Locator = this.page.getByRole('button', { name: 'import ลงทะเบียนแบบกลุ่ม' });
  readonly enrollmentWithRoundOptionLocator: Locator = this.page.getByText('มีรอบการเรียน', { exact: true });
  readonly enrollmentWithoutRoundOptionLocator: Locator = this.page.getByRole('img', {
    name: 'bulk course without round',
  });
  readonly nextButtonLocator: Locator = this.page.getByRole('button', { name: 'ถัดไป' });
  readonly tabSelectEnrollmentDateLocator: Locator = this.page.locator('id=roundDate');
  readonly uploadfileBulkEnrollLocator: Locator = this.page.locator('div.ant-upload-drag-container');
  readonly uploadFileLocator: Locator = this.page
    .getByRole('button', { name: /ส่งมอบรายชื่อ|นำเข้าข้อมูล/, exact: true })
    .nth(1);
  readonly success2EnrollLocator: Locator = this.page.getByText('เพิ่มรายชื่อผู้เข้าเรียนได้สำเร็จ');
  readonly successImportDataLocator: Locator = this.page.getByText('นำเข้าข้อมูลสำเร็จ');
  readonly failedEnrollLocator: Locator = this.page.getByText('ไม่สำเร็จทั้งหมด');
  readonly previousButtonLocator: Locator = this.page.locator('button >> i.fa-arrow-left');
  readonly verifyHeadingEnrollLocator: Locator = this.page.getByRole('heading', {
    name: 'ลงทะเบียนหลักสูตรทั่วไปแบบกลุ่ม',
  });
  readonly searchInputLocator: Locator = this.page.locator('id=fullName');
  readonly columnFullNameLocator: Locator = this.page.locator(
    'td.ant-table-cell:near(th.ant-table-cell:text("คำนำหน้า/ชื่อจริง/ชื่อกลาง/นามสกุล")) >> nth=0',
  );
  readonly columnCourseNameLocator: Locator = this.page.locator(
    'span.ant-typography:near(th.ant-table-cell:text("หลักสูตร")) >> nth=0',
  );

  readonly filterEmailLocator: Locator = this.page.locator('span.body:right-of(span.prefix:text("อีเมล:")) >> nth=0');
  readonly confirmButtonLocator: Locator = this.page.getByRole('button', { name: 'ตกลง' });
  readonly inputExpiredAtLocator: Locator = this.page.locator('#expiredAt');
  readonly tabSelectDateLocator: Locator = this.page.locator('.ant-picker-input');
  readonly nextMonthButtonLocator: Locator = this.page.locator('button[aria-label="next-year"]');
  readonly calendarHeaderLocator: Locator = this.page.locator('button[aria-label="month panel"]');
  readonly submitButtonLocator: Locator = this.page.getByRole('button', {
    name: 'ตกลง',
  });

  readonly statusDropdownLocator: Locator = this.page.locator(
    'div.ant-dropdown-trigger >> span.prefix:has-text("สถานะ")',
  );

  constructor(page: Page) {
    super(page, '/admin');
  }
  readonly req: RegExp = new RegExp('.*/enrollments-v2/learning-enrollments.*');

  async searchFullName(Keyword: string): Promise<this> {
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await expect(this.inputEnrollmentFullNameLocator).toBeVisible();
    await this.inputEnrollmentFullNameLocator.fill(Keyword);
    await this.searchButtonLocator.nth(0).click();
    await this.spinningAnimationSelector.waitFor({ state: 'hidden' });

    return this;
  }

  async searchEmail(Keyword: string): Promise<this> {
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await expect(this.filterEmailLocator).toBeVisible();
    await this.filterEmailLocator.click();
    await this.page.getByPlaceholder('กรอกข้อมูล').fill(Keyword);
    await this.confirmButtonLocator.nth(0).click();
    await this.spinningAnimationSelector.waitFor({ state: 'hidden' });

    return this;
  }

  async searchPreEnrollFullName(Keyword: string): Promise<this> {
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await this.inputPreEnrollmentFullNameLocator.fill(Keyword);
    await this.searchButtonLocator.nth(0).click();
    await this.spinningAnimationSelector.waitFor({ state: 'hidden' });

    return this;
  }

  async searchEnrollFullName(Keyword: string): Promise<this> {
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await this.inputEnrollmentFullNameLocator.fill(Keyword);
    await this.searchButtonLocator.nth(0).click();
    await this.spinningAnimationSelector.waitFor({ state: 'hidden' });

    return this;
  }

  async searchByEmail(email: string): Promise<this> {
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await expect(this.filterEmailLocator).toBeVisible();
    await this.filterEmailLocator.click();
    await this.page.getByPlaceholder('ค้นหา').fill(email);
    await this.submitButtonLocator.click();
    await this.loadingSpinLocator.waitFor({ state: 'hidden' });
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await expect(this.page.locator('.ant-table-cell', { hasText: email })).toBeVisible();
    await this.page.waitForLoadState();
    await this.loadingSkeleton.waitFor({ state: 'hidden' });

    return this;
  }

  async searchRegularLearningByEmail(email: string): Promise<this> {
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await expect(this.filterEmailLocator).toBeVisible();
    await this.filterEmailLocator.click();
    await this.page.getByPlaceholder('กรอกข้อมูล').fill(email);
    await this.submitButtonLocator.click();
    await this.loadingSpinLocator.waitFor({ state: 'hidden' });
    await expect(this.page.locator('.ant-table-cell', { hasText: email })).toBeVisible();
    await this.page.waitForLoadState();
    await this.loadingSkeleton.waitFor({ state: 'hidden' });

    return this;
  }

  async clickEnrollmentDetail(): Promise<this> {
    await this.viewDetailButtonLocator.click();

    return this;
  }

  async clickEnrollmentLearningTab(): Promise<this> {
    await this.enrollmentLearningTabLocator.click();
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await this.loadingAnimation.waitFor({ state: 'hidden' });

    return this;
  }

  async clickLearningTab(): Promise<this> {
    await this.learningTabLocator.click();

    return this;
  }

  async verifyAttentionCheckHistory(): Promise<this> {
    await this.attentionCheckHistoryLocator.isVisible();

    return this;
  }

  async verifyRpcHistory(): Promise<this> {
    await this.rpcHistoryLocator.isVisible();

    return this;
  }

  async searchUsers(name: string): Promise<this> {
    await this.nameInputLocator.fill(name);
    await this.searchButtonLocator.click();
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await this.loadingAnimation.waitFor({ state: 'hidden' });

    return this;
  }

  async viewEnrollmentDetailTLI(): Promise<this> {
    await this.viewDetailButtonLocator.click();

    return this;
  }

  async requestDocument(text: string, date: Date): Promise<this> {
    await this.requestDocumentButtonLocator.click();
    await this.checkBoxCitizenCardLocator.click();
    await this.reasonRequestDocumentInputLocator.fill(text);

    await this.tabSelectDateLocator.click();

    const expectedMonth = CalendarHelper.getFullBuddhistNoSetZeroDateString(date).split(' ')[1];
    const currentMonth = await this.calendarHeaderLocator.textContent();

    if (currentMonth?.trim() !== expectedMonth) {
      await this.nextMonthButtonLocator.click();
    }

    await this.page
      .locator(
        this.selectDateOnCalendarLocator.replace('${date}', CalendarHelper.getBuddhistDateString(new Date(date))),
      )
      .click();

    await expect(this.inputExpiredAtLocator).not.toHaveValue('เลือก วัน/เดือน/ปี ที่ส่งเอกสาร');
    await expect(this.inputExpiredAtLocator).not.toHaveValue('');
    await this.submitRequestDocumentButtonLocator.click();

    return this;
  }

  async verifyEnrollmentCourseVersion(version: string): Promise<this> {
    await this.loadingAnimation.waitFor({ state: 'hidden' });
    await this.page.waitForLoadState();
    await expect(this.verifyCourseVersionLocator).toContainText(version);

    return this;
  }

  async accessBulkPreEnrollPage(): Promise<this> {
    await this.bulkEnrollButtonLocator.click();
    await this.page.waitForURL(new RegExp('.*/selectBulk.*'));
    await this.enrollmentWithRoundOptionLocator.click();
    await this.nextButtonLocator.click();
    await this.page.waitForURL(new RegExp('.*/bulkPreEnrollment.*'));

    return this;
  }

  async accessBulkEnrollWithoutRound(): Promise<this> {
    await this.bulkEnrollButtonLocator.click();
    await this.page.waitForURL(new RegExp('.*/selectBulk'));
    await this.enrollmentWithoutRoundOptionLocator.click();
    await this.nextButtonLocator.click();
    await this.page.waitForURL(new RegExp('.*/bulkEnrollment'));

    return this;
  }

  async selectEnrollmentDate(date: Date): Promise<this> {
    await this.tabSelectEnrollmentDateLocator.click();
    await this.page
      .locator(
        this.selectDateOnCalendarLocator.replace('${date}', CalendarHelper.getBuddhistDateString(new Date(date))),
      )
      .click();

    return this;
  }

  async uploadFile(file: string): Promise<this> {
    const filePath = `${path.join(__dirname, '../../../../', file)}`;
    const [fileChooser] = await Promise.all([
      this.page.waitForEvent('filechooser'),
      this.uploadfileBulkEnrollLocator.click(),
    ]);
    await fileChooser.setFiles(filePath);
    await this.uploadFileLocator.click();

    return this;
  }

  async verifyEnrollSuccess(): Promise<this> {
    await this.success2EnrollLocator.waitFor();

    return this;
  }

  async verifyBulkEnrollImportSuccess(): Promise<this> {
    await this.successImportDataLocator.waitFor();

    return this;
  }

  async verifyEnrollFail(): Promise<this> {
    await this.failedEnrollLocator.waitFor();

    return this;
  }

  async backToEnrollPage(): Promise<this> {
    await this.previousButtonLocator.click();
    await this.uploadFileLocator.waitFor();
    await this.previousButtonLocator.click();
    await this.page.waitForURL(new RegExp('.*/selectBulk.*'));
    await this.verifyHeadingEnrollLocator.waitFor();
    await this.previousButtonLocator.click();

    await this.page.waitForURL(new RegExp('.*/regularEnrollments.*'));
    return this;
  }

  async filterAllEnrollment(): Promise<this> {
    await this.filterStatusLocator.click();
    await this.enrollmentStatusAllLocator.nth(0).click();

    return this;
  }

  async verifyPreEnrollList(name: string, date: Date): Promise<this> {
    await expect(this.page.getByText(name)).toBeVisible();
    await expect(this.page.getByText(`${CalendarHelper.getShortBuddhistDateString(date)}`)).toBeVisible();
    await this.loadingAnimation.waitFor({ state: 'hidden' });
    await this.page.waitForLoadState();
    return this;
  }

  async verifyEnrollmentList(name: string, courseName: string): Promise<this> {
    await expect(this.columnFullNameLocator).toBeVisible();
    await expect(this.columnFullNameLocator).toContainText(name);
    await expect(this.columnCourseNameLocator).toContainText(courseName);

    return this;
  }

  async filterStatus(status: string): Promise<this> {
    await expect(this.statusDropdownLocator).toBeVisible();
    await this.statusDropdownLocator.click();
    await this.filterStatusItemAllLocator.filter({ hasText: status }).first().click();
    await this.spinningAnimationSelector.waitFor({ state: 'hidden' });

    return this;
  }
}
