import { expect, Locator, Page } from '@playwright/test';
import { BasePage } from '../base-page';
import { CalendarHelper } from '../../../../shared/calendar/calendar-helper';

export class TrainingEnrollmentsPage extends BasePage {
  readonly nameInputLocator: Locator = this.page.getByPlaceholder('ชื่อจริง/นามสกุล');
  readonly searchButtonLocator: Locator = this.page.locator('svg[data-icon="search"]');

  readonly waitingToEnrollTabLocator: Locator = this.page.getByRole('tab', { name: 'รอลงเรียน' });
  readonly enrollmentLearningTabLocator: Locator = this.page.getByRole('tab', { name: 'กำลังเรียน' });
  readonly viewDetailButtonLocator: Locator = this.page.getByRole('button', { name: 'ดูรายละเอียด' });
  readonly verifyFullNameLocator: Locator = this.page.locator(
    'div.ant-card-body >> div.ant-space-item >> h5.ant-typography',
  );
  readonly reasonRequestDocumentInputLocator: Locator = this.page.getByPlaceholder('กรอกเหตุผลการขอเอกสาร');

  readonly requestDocumentButtonLocator: Locator = this.page.getByRole('button', { name: 'ขอเอกสาร' });
  readonly checkBoxCitizenCardLocator: Locator = this.page.locator(
    'div:nth-child(4) > .ant-tree-checkbox > .ant-tree-checkbox-inner',
  );
  readonly submitRequestDocumentButtonLocator: Locator = this.page.getByRole('button', { name: 'ยืนยัน' });
  readonly verifyHeadingDocumentLocator: Locator = this.page.getByRole('heading', { name: 'ข้อมูลเอกสารเพิ่มเติม' });
  readonly verifyHeadingRemarkLocator: Locator = this.page.getByRole('heading', { name: 'หมายเหตุ' });
  readonly editRemarkButtonLocator: Locator = this.page.getByRole('button', { name: 'แก้ไข' });
  readonly remarkInputLocator: Locator = this.page.getByPlaceholder('กรอกหมายเหตุ');
  readonly submitRemarkButtonLocator: Locator = this.page.getByRole('button', { name: 'บันทึก' });
  readonly remarkDisplayContentLocator: Locator = this.page
    .locator('div:below(h3:has-text("หมายเหตุ")) >> span:not(:has(button)), div:below(h3:has-text("หมายเหตุ")) >> p')
    .first();
  readonly verifyHeadingCreditHistoryLocator: Locator = this.page.getByRole('heading', { name: 'การใช้งานเครดิต' });
  readonly searchFullnNameLocator: Locator = this.page.locator('#enrollmentFullName');
  readonly searchResultLocator: Locator = this.page.getByRole('heading', { name: 'ผลการค้นหา:' });
  readonly columnFullNameLocator: Locator = this.page.locator(
    'td.ant-table-cell:below(th.ant-table-cell:text("คำนำหน้า/ชื่อจริง/ชื่อกลาง/นามสกุล")) >> nth=0',
  );
  readonly columnCourseNameLocator: Locator = this.page.locator(
    'td.ant-table-cell:below(th.ant-table-cell:text("หลักสูตร")) >> nth=0',
  );
  readonly searchEmailLocator: Locator = this.page.locator('div.ant-space-item >> span.prefix:has-text("อีเมล")');
  readonly inputEmailLocator: Locator = this.page.locator('input#inputText');
  readonly inputExpiredAtLocator: Locator = this.page.locator('#expiredAt');
  readonly tabSelectDateLocator: Locator = this.page.locator('.ant-picker-input');
  readonly nextMonthButtonLocator: Locator = this.page.locator('button[aria-label="next-year"]');
  readonly calendarHeaderLocator: Locator = this.page.locator('button[aria-label="month panel"]');
  private readonly selectDateOnCalendarLocator: string = 'css=td[title="${date}"] >> nth=0';
  readonly enrollmentStatusOnFooterLocator: Locator = this.page
    .locator('footer.ant-layout-footer >> div.ant-space-item:right-of(span:has-text("สถานะ:"))')
    .first();

  // Check state
  readonly loadingSkeleton: Locator = this.page
    .locator('td > .ant-skeleton > .ant-skeleton-content > .ant-skeleton-title')
    .first();
  readonly toastMessageSuccessLocator: Locator = this.page.getByText('สร้างรายการขอเอกสารเพิ่มเติมสำเร็จ');

  constructor(page: Page) {
    super(page, '/admin/additionalDocuments');
  }

  // Component admin
  async clickWaitingToEnrollTab(): Promise<this> {
    await this.waitingToEnrollTabLocator.click();
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await this.loadingAnimation.waitFor({ state: 'hidden' });

    return this;
  }

  async clickEnrollmentLearningTab(): Promise<this> {
    await this.enrollmentLearningTabLocator.click();
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await this.loadingAnimation.waitFor({ state: 'hidden' });

    return this;
  }

  async searchUsers(name: string): Promise<this> {
    await this.nameInputLocator.fill(name);
    await this.searchButtonLocator.click();
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await this.loadingAnimation.waitFor({ state: 'hidden' });

    return this;
  }

  async viewEnrollmentDetailTLI(): Promise<this> {
    await this.viewDetailButtonLocator.click();

    return this;
  }

  async viewEnrollmentDetail(): Promise<this> {
    await this.viewDetailButtonLocator.click();
    await Promise.all([
      await this.page.waitForLoadState(),
      await expect(this.verifyHeadingDocumentLocator).toBeVisible(),
      await expect(this.verifyHeadingCreditHistoryLocator).toBeVisible(),
    ]);

    return this;
  }

  async requestDocument(text: string, date: Date): Promise<this> {
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await this.requestDocumentButtonLocator.click();
    await this.checkBoxCitizenCardLocator.click();
    await this.reasonRequestDocumentInputLocator.fill(text);

    await this.tabSelectDateLocator.click();

    const expectedMonth = CalendarHelper.getFullBuddhistNoSetZeroDateString(date).split(' ')[1];
    const currentMonth = await this.calendarHeaderLocator.textContent();

    if (currentMonth?.trim() !== expectedMonth) {
      await this.nextMonthButtonLocator.click();
    }

    await this.page
      .locator(
        this.selectDateOnCalendarLocator.replace('${date}', CalendarHelper.getBuddhistDateString(new Date(date))),
      )
      .click();

    await expect(this.inputExpiredAtLocator).not.toHaveValue('เลือก วัน/เดือน/ปี ที่ส่งเอกสาร');
    await expect(this.inputExpiredAtLocator).not.toHaveValue('');

    await this.submitRequestDocumentButtonLocator.click();
    await expect(this.toastMessageSuccessLocator).toBeVisible();

    return this;
  }

  async searchNameAndVerifyUserEnrollmentCourse(
    firstname: string,
    lastname: string,
    courseName: string,
  ): Promise<this> {
    await this.searchFullnNameLocator.fill(`${firstname} ${lastname}`);
    await this.page.keyboard.press('Enter');
    await this.loadingAnimation.waitFor({ state: 'hidden' });
    await this.searchResultLocator.waitFor();
    await expect(this.columnFullNameLocator).toContainText(`${firstname}${lastname}`);
    await expect(this.columnCourseNameLocator).toContainText(courseName);

    return this;
  }
  async verifyEnrollmentStatus(status: string): Promise<this> {
    await expect(this.enrollmentStatusOnFooterLocator).toBeVisible();
    switch (status) {
      case 'in_progress': {
        await expect(this.enrollmentStatusOnFooterLocator).toContainText('กำลังเรียน');
        break;
      }
      default: {
        /* empty */
      }
    }

    return this;
  }

  async editRemark(remark: string): Promise<this> {
    await this.editRemarkButtonLocator.click();
    await this.remarkInputLocator.fill(remark);
    await this.submitRemarkButtonLocator.click();
    await expect(this.toastMessageSuccessLocator).toBeVisible();

    return this;
  }

  async verifyRemarkContent(expectedRemark: string): Promise<this> {
    // Try primary locator first
    try {
      await expect(this.remarkDisplayContentLocator).toBeVisible({ timeout: 5000 });
      await expect(this.remarkDisplayContentLocator).toContainText(expectedRemark);
    } catch (error) {
      // Fallback: look for the remark text anywhere on the page near the remark section
      const fallbackLocator = this.page.getByText(expectedRemark).first();
      await expect(fallbackLocator).toBeVisible();
    }

    return this;
  }
}
