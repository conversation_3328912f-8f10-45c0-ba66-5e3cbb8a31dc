import { expect } from '@playwright/test';
import { v4 as uuidv4 } from 'uuid';
import { test as base } from '../../fixtures/default-fixture';
import { RoundsRepo } from '../../../shared/repositories/lms/repo/rounds-repo/round-repo';
import { PreEnrollmentReservationRepo } from '../../../shared/repositories/lms/repo/pre-enrollments-repo/pre-enrollment-reservation-repo';
import { UsersRepo } from '../../../shared/repositories/lms/repo/users-repo/users-repo';
import { EnrollmentsRepo } from '../../../shared/repositories/lms/repo/enrollments-repo/enrollments-repo';
import { ExcelTestData } from '../../../shared/excel/excel-test-data';
import { PreEnrollmentTransactionRepo } from '../../../shared/repositories/lms/repo/pre-enrollments-repo/pre-enrollment-transaction-repo';
import { InvoiceRepo } from '../../../shared/repositories/lms/repo/credits-repo/invoices-repo';
import { InvoicePage } from '../../elements/pages/backoffice/invoice-page';
import { CalendarHelper } from '../../../shared/calendar/calendar-helper';

let resetTimestamp: number;

const deleteReservedCreditItem = async (
  preEnrollmentReservationRepo: PreEnrollmentReservationRepo,
  customerCode: string,
) => {
  await preEnrollmentReservationRepo.deleteByCustomerCode(customerCode);
};

const deleteInvoiceList = async (invoiceRepo: InvoiceRepo, customerCode: string) => {
  await invoiceRepo.deleteByCustomerCode(customerCode);
};

export const test = base.extend<{
  bulkEnrollExcel: ExcelTestData;
  bulkPreEnrollExcel: ExcelTestData;
}>({
  bulkPreEnrollExcel: async ({ cpdExcelTemplateService }, use) => {
    const ExcelTestData = await cpdExcelTemplateService.getExcelPreEnrollmentTemplate();
    use(ExcelTestData);
  },
  bulkEnrollExcel: async ({ cpdExcelTemplateService }, use) => {
    const ExcelTestData = await cpdExcelTemplateService.getExcelBulkEnrollTemplate();
    use(ExcelTestData);
  },
});

async function cleanUpRoundAndEnrollment(
  roundRepo: RoundsRepo,
  usersRepo: UsersRepo,
  enrollmentsRepo: EnrollmentsRepo,
  preEnrollmentTransactionRepo: PreEnrollmentTransactionRepo,
  preEnrollmentReservationRepo: PreEnrollmentReservationRepo,
  invoiceRepo: InvoiceRepo,
  email: string,
  roundDate: Date,
  course: string,
  company: string,
) {
  const lmsUser = await usersRepo.getByEmail(email);

  // Clean up enrollment
  await enrollmentsRepo.deleteAllEnrollmentsForUser(lmsUser.guid);

  // Clean Pre-Enrollment Transaction
  await preEnrollmentTransactionRepo.deleteByPayloadEmail(lmsUser.email);

  // Clean reserved credit item
  await deleteReservedCreditItem(preEnrollmentReservationRepo, company);

  // Clean Credit Use List
  await deleteInvoiceList(invoiceRepo, company);

  // Clean up round
  await roundRepo.deleteTrainingRoundByTrainingDateAndCourseId(course, roundDate);
}

test.beforeEach(
  async ({
    roundRepo,
    configuration,
    usersRepo,
    enrollmentsRepo,
    preEnrollmentTransactionRepo,
    preEnrollmentReservationRepo,
    invoiceRepo,
  }) => {
    const courses = [
      configuration.shareCourses.courseOICCredit,
      configuration.shareCourses.courseBundle.courseContent1,
      configuration.shareCourses.courseBundle.courseContent2,
    ];
    const lmsUsers = configuration.usersLocal.userSKL1;
    const company = configuration.customers.company2;
    const currentDate = new Date();
    const roundDates = [
      new Date(new Date().setDate(currentDate.getDate() + 4)),
      new Date(new Date().setDate(currentDate.getDate() + 0)),
    ];
    roundDates.forEach((date) => date.setHours(0, 0, 0, 0));
    resetTimestamp = new Date().getTime();

    // Clean data enrollment and round
    for (const roundDate of roundDates) {
      for (const course of courses) {
        await cleanUpRoundAndEnrollment(
          roundRepo,
          usersRepo,
          enrollmentsRepo,
          preEnrollmentTransactionRepo,
          preEnrollmentReservationRepo,
          invoiceRepo,
          lmsUsers.email,
          roundDate,
          course.courseId,
          company.code,
        );
      }
    }
  },
);

test.afterEach(
  async ({
    roundRepo,
    configuration,
    usersRepo,
    enrollmentsRepo,
    preEnrollmentTransactionRepo,
    preEnrollmentReservationRepo,
    invoiceRepo,
  }) => {
    const courses = [
      configuration.shareCourses.courseOICCredit,
      configuration.shareCourses.courseBundle.courseContent1,
      configuration.shareCourses.courseBundle.courseContent2,
    ];
    const lmsUsers = configuration.usersLocal.userSKL1;
    const company = configuration.customers.company2;
    const currentDate = new Date();
    const roundDates = [
      new Date(new Date().setDate(currentDate.getDate() + 4)),
      new Date(new Date().setDate(currentDate.getDate() + 0)),
    ];
    roundDates.forEach((date) => date.setHours(0, 0, 0, 0));

    // Clean data enrollment and round
    for (const roundDate of roundDates) {
      for (const course of courses)
        await cleanUpRoundAndEnrollment(
          roundRepo,
          usersRepo,
          enrollmentsRepo,
          preEnrollmentTransactionRepo,
          preEnrollmentReservationRepo,
          invoiceRepo,
          lmsUsers.email,
          roundDate,
          course.courseId,
          company.code,
        );
    }
  },
);
test.describe('Admin - Pre-enrollment', () => {
  test('Admin pre-enrollment to learner and verify company reserved credit', async ({
    roundRepo,
    configuration,
    loginCpdPage,
    bulkPreEnrollExcel,
    adminDashboardPage,
    waitingToEnrollPage,
    manageCompanyPage,
    testmailAppClient,
  }) => {
    const company = configuration.customers.company2;
    const adminCPD = configuration.usersLocal.organizationAdminSKL1;
    const userCPD = configuration.usersLocal.userSKL1;
    const courseCPD = configuration.shareCourses.courseOICCredit;
    const courseCPDVersion1 = courseCPD.courseVersions[1];
    const currentDate = new Date();
    const roundDate = new Date(new Date().setDate(currentDate.getDate() + 4));
    roundDate.setHours(0, 0, 0, 0);
    const firstRegisDate = new Date(new Date().setDate(currentDate.getDate() - 0));
    const lastRegisDate = new Date(new Date().setDate(currentDate.getDate() + 2));
    const expiryDate = new Date(roundDate);
    expiryDate.setDate(roundDate.getDate() + courseCPDVersion1.expiryDay);

    //Seed round
    await roundRepo.create({
      id: uuidv4(),
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      courseIds: courseCPD.courseId,
      createdAt: currentDate,
      updatedAt: currentDate,
    });

    // Create bulk enroll template
    bulkPreEnrollExcel.addRow({
      email: userCPD.email,
      prefix: userCPD.salute,
      firstname: userCPD.firstname,
      lastname: userCPD.lastname,
      id_card_no: userCPD.citizenId,
      course_code: courseCPD.courseCode,
    });
    await bulkPreEnrollExcel.writeExcelFile('user-validation-template.xlsx');

    // Admin bulk pre-enrollment
    await loginCpdPage.accessLoginSSO();
    await loginCpdPage.loginWithUsernameAndPassword(adminCPD.username, adminCPD.password);
    await expect(adminDashboardPage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessManageOICEnrollment();
    await waitingToEnrollPage.accessBulkPreEnrollPage();
    await waitingToEnrollPage.selectEnrollmentDate(roundDate);
    await waitingToEnrollPage.selectCompanyCPD(company.code);
    await waitingToEnrollPage.selectCustomerPartnerCPD(company.partnerCode);
    await waitingToEnrollPage.uploadFile('user-validation-template.xlsx');
    await waitingToEnrollPage.verifySuccessAndBackToEnrollPage();

    // Admin verify reserved credit items
    await adminDashboardPage.accessManageCompany();
    await manageCompanyPage.editCompanyByCompanyCode(company.code);
    await manageCompanyPage.accessManageCreditTab();
    await manageCompanyPage.verifyReservedCreditItems(
      company.creditDetail.purchaseOrderId,
      company.creditDetail.subPurchaseOrderId,
    );

    // Verify participant email
    const email = await testmailAppClient.fetchLatestEmailInbox(
      userCPD.email,
      `การลงทะเบียนล่วงหน้าสำหรับหลักสูตร “${courseCPDVersion1.name}” สำเร็จ | SkillLaneCPD`,
      resetTimestamp,
    );
    expect(email.html).toContain(
      `หน่วยงานต้นสังกัดได้ลงทะเบียนล่วงหน้าสำหรับหลักสูตร “${courseCPDVersion1.name}” รอบวันที่ ${CalendarHelper.getFullBuddhistNoSetZeroDateString(roundDate)} เวลา 9:00 น. ถึง ${CalendarHelper.getFullBuddhistNoSetZeroDateString(expiryDate)} เวลา 23:59 น. ให้คุณสำเร็จ`,
    );
  });

  test.skip('Admin able to refund credit and cancel enrollment from customer type B2B', async ({
    //TODO:wait for fix viewInvoice
    roundRepo,
    configuration,
    loginCpdPage,
    adminDashboardPage,
    trainingEnrollmentsPage,
    waitingToEnrollPage,
    bulkEnrollExcel,
    manageCompanyPage,
    testmailAppClient,
    page,
  }) => {
    const company = configuration.customers.company2;
    const adminCPD = configuration.usersLocal.organizationAdminSKL1;
    const userCPD = configuration.usersLocal.userSKL1;
    const courseCPD = configuration.shareCourses.courseOICCredit;
    const courseCPDVersion1 = courseCPD.courseVersions[1];
    const currentDate = new Date();
    const roundDate = new Date(new Date().setDate(currentDate.getDate() + 0));
    roundDate.setHours(0, 0, 0, 0);
    const firstRegisDate = new Date(new Date().setDate(currentDate.getDate() - 5));
    const lastRegisDate = new Date(new Date().setDate(currentDate.getDate() - 2));
    const expiryDate = new Date(new Date().setDate(roundDate.getDate() + courseCPDVersion1.expiryDay));

    //Seed round
    await roundRepo.create({
      id: uuidv4(),
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      courseIds: courseCPD.courseId,
      createdAt: currentDate,
      updatedAt: currentDate,
    });

    // Create bulk enroll template
    bulkEnrollExcel.addRow({
      userName: userCPD.username,
      productSKUCode: courseCPD.courseCode,
      business: 'B2B',
      customerCode: company.code,
      customerPartnerCode: company.partnerCode,
    });

    await bulkEnrollExcel.writeExcelFile('bulk-enrollment-template.xlsx');

    // Admin Enrollment step by pass
    await loginCpdPage.accessLoginSSO();
    await loginCpdPage.loginWithUsernameAndPassword(adminCPD.username, adminCPD.password);
    await expect(adminDashboardPage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessManageOICEnrollment();
    await trainingEnrollmentsPage.clickWaitingToEnrollTab();
    await waitingToEnrollPage.accessBulkEnrollByPass();
    await waitingToEnrollPage.selectEnrollTypeCourse();
    await waitingToEnrollPage.selectEnrollmentDate(roundDate);
    await waitingToEnrollPage.uploadFile('bulk-enrollment-template.xlsx');
    await waitingToEnrollPage.verifyEnrollmentCourseCPDSuccess();

    // Verify participant email
    const email = await testmailAppClient.fetchLatestEmailInbox(
      userCPD.email,
      `การลงทะเบียนเรียนหลักสูตร “${courseCPDVersion1.name}” สำเร็จ | SkillLaneCPD`,
      resetTimestamp,
    );
    expect(email.html).toContain(
      `คุณได้ลงทะเบียนเรียนหลักสูตร “${courseCPDVersion1.name}” สำเร็จ โดยสามารถเข้าเรียนได้ถึงวันที่ ${CalendarHelper.getFullBuddhistNoSetZeroDateString(expiryDate)} เวลา 23:59 น. หากเกินระยะเวลาที่กำหนดจะไม่สามารถเข้าเรียนได้`,
    );

    // Admin verify invoice and refund credit
    await waitingToEnrollPage.clickEnrollmentOICMenu();
    await trainingEnrollmentsPage.clickEnrollmentLearningTab();
    await trainingEnrollmentsPage.searchUsers(userCPD.firstname);
    await trainingEnrollmentsPage.viewEnrollmentDetail();
    await page.pause();

    const newPage = await manageCompanyPage.viewInvoice();
    const invoicePage = new InvoicePage(newPage);
    await invoicePage.refundCredit();
    await invoicePage.successRefundCredit();

    // Verify participant email
    const emailCancel = await testmailAppClient.fetchLatestEmailInbox(
      userCPD.email,
      `ยกเลิกการอบรมหลักสูตร “${courseCPDVersion1.name}” เรียบร้อยแล้ว | SkillLaneCPD`,
      resetTimestamp,
    );
    expect(emailCancel.html).toContain(
      `ระบบได้ทำการยกเลิกการอบรม “${courseCPDVersion1.name}” เรียบร้อยแล้ว หลังจากนี้ คุณจะไม่สามารถเข้าอบรมหลักสูตรนี้ต่อได้`,
    );
  });

  test.skip('Admin able to pre-enrollment bundle course', async ({
    roundRepo,
    configuration,
    loginCpdPage,
    bulkPreEnrollExcel,
    adminDashboardPage,
    waitingToEnrollPage,
  }) => {
    const company = configuration.customers.company2;
    const adminCPD = configuration.usersLocal.organizationAdminSKL1;
    const userCPD = configuration.usersLocal.userSKL1;
    const bundleCourse = configuration.shareCourses.courseBundle;
    const courses = [
      configuration.shareCourses.courseBundle.courseContent1,
      configuration.shareCourses.courseBundle.courseContent2,
    ];
    const currentDate = new Date();
    const roundDate = new Date(new Date().setDate(currentDate.getDate() + 4));
    roundDate.setHours(0, 0, 0, 0);
    const firstRegisDate = new Date(new Date().setDate(currentDate.getDate() - 0));
    const lastRegisDate = new Date(new Date().setDate(currentDate.getDate() + 2));

    const courseIds = courses.map((course) => course.courseId);

    //Seed round
    await roundRepo.create({
      id: uuidv4(),
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      courseIds: courseIds,
      createdAt: currentDate,
      updatedAt: currentDate,
    });

    // Create bulk enroll template
    bulkPreEnrollExcel.addRow({
      email: userCPD.email,
      prefix: userCPD.salute,
      firstname: userCPD.firstname,
      lastname: userCPD.lastname,
      id_card_no: userCPD.citizenId,
      course_code: bundleCourse.bundleCode,
    });
    await bulkPreEnrollExcel.writeExcelFile('user-validation-template.xlsx');

    // Admin bulk pre-enrollment
    await loginCpdPage.accessLoginSSO();
    await loginCpdPage.fillUsername(adminCPD.username);
    await loginCpdPage.fillPassword(adminCPD.password);
    await loginCpdPage.submitButton();
    await expect(adminDashboardPage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessManageOICEnrollment();
    await waitingToEnrollPage.accessBulkPreEnrollPage();
    await waitingToEnrollPage.selectEnrollmentDate(roundDate);
    await waitingToEnrollPage.selectCompanyCPD(company.code);
    await waitingToEnrollPage.selectCustomerPartnerCPD(company.partnerCode);
    await waitingToEnrollPage.uploadFile('user-validation-template.xlsx');

    // Admin verify pre-enrollment detail
    await waitingToEnrollPage.verifySuccessAndBackToEnrollPage();
    await waitingToEnrollPage.verifyPreEnroll(userCPD.firstname, roundDate);
    await waitingToEnrollPage.viewPreEnrollmentDetail();
    await waitingToEnrollPage.verifyPreEnrollDetailCPD(bundleCourse.name, company.code);
  });
});
