// import { expect } from '@playwright/test';
// import { v4 as uuidv4 } from 'uuid';
// import { test } from '../../../../fixtures/default-fixture';
// import { EnrollmentsRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/enrollments-repo';
// import { EnrollType } from '../../../../../shared/repositories/lms/constants/enums/enroll-type.enum';

// let uniqueEnrollmentId: string;
// let enrollment: any;
// let round: any;

// test.beforeAll(async ({ configuration, enrollmentsRepo, roundRepo }) => {
//   const userTLI = configuration.shareUsers.userCommentTLI;
//   const course = configuration.shareCourses.oicCourseManualApprove;

//   uniqueEnrollmentId = uuidv4();
//   const date = new Date();
//   const expiredAt = new Date();
//   expiredAt.setDate(date.getDate() + 30);

//   round = await roundRepo.create({
//     id: uuidv4(),
//     courseId: course.courseId,
//     courseVersionId: course.courseVersions[1].id,
//     firstRegistrationDate: date,
//     lastRegistrationDate: expiredAt,
//     trainingDate: expiredAt,
//     createdAt: date,
//     updatedAt: date,
//   });

//   enrollment = await enrollmentsRepo.create({
//     id: uniqueEnrollmentId,
//     courseId: course.courseId,
//     courseVersionId: course.courseVersions[1].id,
//     organizationId: userTLI.organizationId,
//     userId: userTLI.guid,
//     business: EnrollmentsRepo.BUSINESS.b2c,
//     status: EnrollmentsRepo.STATUS.inProgress,
//     isCountdownArticle: false,
//     roundId: round.id,
//     completedCourseItem: 0,
//     learningProgress: [],
//     startedAt: date,
//     expiredAt: expiredAt,
//     acceptedAt: null,
//     createdAt: date,
//     updatedAt: date,
//     isIdentityVerificationEnabled: true,
//     remark: '',
//     externalContentType: '',
//     enrollType: EnrollType.voluntary,
//     customerCode: '',
//   });
// });

// test.afterAll(async ({ enrollmentsRepo, roundRepo, configuration }) => {
//   const course = configuration.shareCourses.oicCourseManualApprove;

//   if (uniqueEnrollmentId) {
//     await enrollmentsRepo.deleteById(uniqueEnrollmentId);
//   }

//   // Clean up round
//   if (round && round.id) {
//     await roundRepo.deleteRoundByCourseId(course.courseId);
//   }
// });

// test.beforeEach(async ({ configuration, loginPage }) => {
//   const adminTLI = configuration.shareUsers.userAdminReplyTLI;

//   await loginPage.fillUsername(adminTLI.username);
//   await loginPage.fillPassword(adminTLI.password);
//   await loginPage.submit();
// });

// test.describe('Document Management - Enrollment Detail', () => {
//   test('@SKL-T20021 Admin ทำการขอเอกสารเพิ่มเติมจากผู้เรียนที่มีสถานะกำลังเรียน, ผู้เรียนได้รับแจ้งเตือนบนระบบการขอเอกสาร', async ({
//     configuration,
//     adminDashboardPage,
//     trainingEnrollmentsPage,
//     page,
//   }) => {
//     const learnerTLI = configuration.shareUsers.userCommentTLI;
//     const course = configuration.shareCourses.oicCourseManualApprove;
//     const resetTimestamp = new Date().getTime();

//     await adminDashboardPage.accessManageOICEnrollment();
//     await page.pause();
//     await trainingEnrollmentsPage.clickEnrollmentLearningTab();
//     await trainingEnrollmentsPage.searchNameAndVerifyUserEnrollmentCourse(
//       learnerTLI.firstname,
//       learnerTLI.lastname,
//       course.courseVersions[1].name,
//     );
//     await trainingEnrollmentsPage.viewDetailButtonLocator.click();
//     await trainingEnrollmentsPage.verifyEnrollmentStatus('in_progress');

//     // VERIFY STATUS
//   });
// });

import { expect } from '@playwright/test';
import { v4 as uuidv4 } from 'uuid';
import { test } from '../../../../fixtures/default-fixture';
import { RoundsRepo } from '../../../../../shared/repositories/lms/repo/rounds-repo/round-repo';
import { UsersRepo } from '../../../../../shared/repositories/lms/repo/users-repo/users-repo';
import { EnrollmentsRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/enrollments-repo';
import { EnrollmentCertificatesRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/enrollment-certificates-repo';
import { EnrollmentAttachmentsRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/enrollment-attachments-repo';
import { QuizAnswersRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/quiz-answers-repo';
import { PreEnrollmentTransactionRepo } from '../../../../../shared/repositories/lms/repo/pre-enrollments-repo/pre-enrollment-transaction-repo';
import { EnrollType } from '../../../../../shared/repositories/lms/constants/enums/enroll-type.enum';

let resetTimestamp: number;

async function cleanUpRoundAndEnrollment(
  roundRepo: RoundsRepo,
  usersRepo: UsersRepo,
  enrollmentsRepo: EnrollmentsRepo,
  enrollmentCertificatesRepo: EnrollmentCertificatesRepo,
  enrollmentAttachmentsRepo: EnrollmentAttachmentsRepo,
  quizAnswersRepo: QuizAnswersRepo,
  preEnrollmentTransactionRepo: PreEnrollmentTransactionRepo,
  email: string,
  roundDate: Date,
  course: string,
) {
  const lmsUser = await usersRepo.getByEmail(email);
  const userEnrollments = await enrollmentsRepo.getAllEnrollmentsForUser(lmsUser.guid);

  // Clean up round
  await roundRepo.deleteTrainingRoundByTrainingDateAndCourseId(course, roundDate);

  // Clean up enrollment
  for (const userEnrollment of userEnrollments) {
    await enrollmentCertificatesRepo.deleteAllEnrollmentCertificatesForEnrollment(userEnrollment.id);
    await enrollmentAttachmentsRepo.deleteAllEnrollmentAttachedForEnrollment(userEnrollment.id);
    await quizAnswersRepo.deleteManyByEnrollmentId(userEnrollment.id);
  }
  await enrollmentsRepo.deleteAllEnrollmentsForUser(lmsUser.guid);

  // Clean Pre-Enrollment Transaction
  await preEnrollmentTransactionRepo.deleteByPayloadEmail(lmsUser.email);

  // Clean user return URL
  await usersRepo.clearUserReturnUrl(lmsUser.email);
  resetTimestamp = new Date().getTime();
}

test.beforeEach(
  async ({
    roundRepo,
    configuration,
    usersRepo,
    enrollmentsRepo,
    enrollmentCertificatesRepo,
    enrollmentAttachmentsRepo,
    quizAnswersRepo,
    preEnrollmentTransactionRepo,
  }) => {
    const courses = [
      configuration.shareCourses.courseOICAdditionalDoc,
      configuration.shareCourses.courseRegularAdditionalDoc,
    ];
    const lmsUsers = [configuration.shareUsers.userSendDocument];
    const currentDate = new Date();
    const roundDate = new Date(new Date().setDate(currentDate.getDate() + 2));
    roundDate.setHours(0, 0, 0, 0);
    const firstRegisDate = new Date(new Date().setDate(currentDate.getDate() - 1));
    const lastRegisDate = new Date(new Date().setDate(currentDate.getDate() + 1));

    // Clean data enrollment and round
    for (const course of courses) {
      for (const user of lmsUsers) {
        await cleanUpRoundAndEnrollment(
          roundRepo,
          usersRepo,
          enrollmentsRepo,
          enrollmentCertificatesRepo,
          enrollmentAttachmentsRepo,
          quizAnswersRepo,
          preEnrollmentTransactionRepo,
          user.email,
          roundDate,
          course.courseId,
        );
      }
    }

    //Seed round
    for (const course of courses) {
      await roundRepo.create({
        id: uuidv4(),
        roundDate: roundDate,
        firstRegistrationDate: firstRegisDate,
        lastRegistrationDate: lastRegisDate,
        courseIds: course.courseId,
        createdAt: currentDate,
        updatedAt: currentDate,
      });
    }
  },
);

test.afterEach(
  async ({
    roundRepo,
    configuration,
    usersRepo,
    enrollmentsRepo,
    enrollmentCertificatesRepo,
    enrollmentAttachmentsRepo,
    quizAnswersRepo,
    preEnrollmentTransactionRepo,
  }) => {
    const courses = [
      configuration.shareCourses.courseOICAdditionalDoc,
      configuration.shareCourses.courseRegularAdditionalDoc,
    ];
    const lmsUsers = [configuration.shareUsers.userSendDocument];
    const currentDate = new Date();
    const roundDate = new Date(new Date().setDate(currentDate.getDate() + 2));
    roundDate.setHours(0, 0, 0, 0);

    // Clean data enrollment and round
    for (const course of courses) {
      for (const user of lmsUsers) {
        await cleanUpRoundAndEnrollment(
          roundRepo,
          usersRepo,
          enrollmentsRepo,
          enrollmentCertificatesRepo,
          enrollmentAttachmentsRepo,
          quizAnswersRepo,
          preEnrollmentTransactionRepo,
          user.email,
          roundDate,
          course.courseId,
        );
      }
    }
  },
);

test.describe('Admin - Manage enrollment course detail', () => {
  test('@SKL-T20021 Admin able to request additional documents and review documents after user submitted for OIC', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    trainingEnrollmentsPage,
    attachmentPage,
    additionalDocumentsPage,
    roundRepo,
    enrollmentsRepo,
    testmailAppClient,
  }) => {
    const userSendDocument = configuration.shareUsers.userSendDocument;
    const course = configuration.shareCourses.courseOICAdditionalDoc;
    const oicCourseVersions = configuration.courseVersions.oicCourse2Version1;
    const adminRequestDocument = configuration.shareUsers.adminRequestDocument;

    const currentDate = new Date();
    const roundDate = currentDate;
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(23, 59, 0, 0);
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const selectCalendar = new Date(new Date().setMonth(currentDate.getMonth() + 1));

    // Seed round
    const round = await roundRepo.create({
      id: uuidv4(),
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: currentDate,
      updatedAt: currentDate,
      courseIds: oicCourseVersions.courseId,
      organizationId: userSendDocument.organizationId,
    });

    // Seed enrollment course OIC
    await enrollmentsRepo.create({
      id: uuidv4(),
      userId: userSendDocument.guid,
      status: EnrollmentsRepo.STATUS.inProgress,
      createdAt: currentDate,
      updatedAt: currentDate,
      startedAt: currentDate,
      expiredAt: expiredAt,
      completedCourseItem: 0,
      learningProgress: [],
      business: EnrollmentsRepo.BUSINESS.b2c,
      roundId: round.id,
      isCountdownArticle: false,
      isIdentityVerificationEnabled: true,
      organizationId: userSendDocument.organizationId,
      courseId: course.courseId,
      courseVersionId: oicCourseVersions.id,
      approvalAt: null,
      acceptedAt: null,
    });

    // Admin Login Request Additional Document OIC
    await loginPage.loginWithUsernameAndPassword(adminRequestDocument.username, adminRequestDocument.password);
    await adminDashboardPage.accessManageOICEnrollment();
    await trainingEnrollmentsPage.clickEnrollmentLearningTab();
    await trainingEnrollmentsPage.searchUsers(userSendDocument.firstname + ' ' + userSendDocument.lastname);
    await trainingEnrollmentsPage.viewEnrollmentDetailTLI();
    await expect(trainingEnrollmentsPage.verifyFullNameLocator).toBeVisible();
    await trainingEnrollmentsPage.requestDocument('ขาดเอกสารบัตรประชาชนยืนยันตน', selectCalendar);
    await homePage.logout();

    //User receive email
    const email = await testmailAppClient.fetchLatestEmailInbox(
      userSendDocument.email,
      '*ขอเอกสารเพิ่มเติมสำหรับหลักสูตร*',
      resetTimestamp,
    );
    expect(email.html).toContain('ขอเอกสารเพิ่มเติม');

    // User Login Check List Document
    await loginPage.loginWithUsernameAndPassword(userSendDocument.username, userSendDocument.password);
    await attachmentPage.viewListDocument(userSendDocument.citizenId);
    await attachmentPage.uploadFileDocument(configuration.ekycs.uploadProfile);
    await attachmentPage.fillInformation();
    await attachmentPage.confirmInformation();
    await homePage.logoutOnDropdown();

    // Admin Login Verify Additional Document
    await loginPage.loginWithUsernameAndPassword(adminRequestDocument.username, adminRequestDocument.password);
    await adminDashboardPage.accessManageAdditionalDocuments();
    await additionalDocumentsPage.viewUserAdditionalDocumentDetail(
      userSendDocument.firstname + ' ' + userSendDocument.lastname,
    );
    await additionalDocumentsPage.approveDocument();
    await homePage.logout();
  });

  test('@SKL-T20021 Admin able to request additional documents and review documents after user submitted for Regular', async ({
    configuration,
    loginPage,
    homePage,
    adminDashboardPage,
    regularEnrollmentPage,
    attachmentPage,
    additionalDocumentsPage,
    roundRepo,
    enrollmentsRepo,
    testmailAppClient,
  }) => {
    const userSendDocument = configuration.shareUsers.userSendDocument;
    const course = configuration.shareCourses.courseRegularAdditionalDoc;
    const regularCourseVersions = configuration.courseVersions.regularCourse2Version1;
    const adminRequestDocument = configuration.shareUsers.adminRequestDocument;

    const currentDate = new Date();
    const passedDate = new Date(new Date().setDate(currentDate.getMonth() - 2));

    const expiredAt = new Date(new Date().setMonth(currentDate.getMonth() + 1));
    const passedRoundDate = new Date(new Date().setMonth(currentDate.getMonth() - 0));
    const firstRegisDate = new Date(new Date().setMonth(currentDate.getMonth() - 3));
    const lastRegisDate = new Date(new Date().setMonth(currentDate.getMonth() - 2));

    const selectCalendar = new Date(new Date().setMonth(currentDate.getMonth() + 1));

    passedRoundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(23, 59, 0, 0);

    // Seed round passed
    const round = await roundRepo.create({
      id: uuidv4(),
      roundDate: passedRoundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      courseIds: regularCourseVersions.courseId,
      createdAt: currentDate,
      updatedAt: currentDate,
    });

    // Seed enrollment regular passed
    await enrollmentsRepo.create({
      id: uuidv4(),
      userId: userSendDocument.guid,
      status: EnrollmentsRepo.STATUS.inProgress,
      createdAt: passedDate,
      updatedAt: passedDate,
      startedAt: passedRoundDate,
      expiredAt: expiredAt,
      completedCourseItem: 0,
      learningProgress: [],
      business: EnrollmentsRepo.BUSINESS.b2c,
      roundId: round.id,
      isCountdownArticle: false,
      isIdentityVerificationEnabled: true,
      organizationId: userSendDocument.organizationId,
      courseId: course.courseId,
      courseVersionId: regularCourseVersions.id,
      approvalAt: null,
      acceptedAt: null,
    });

    // Admin Login Request Additional Document Regular
    await loginPage.loginWithUsernameAndPassword(adminRequestDocument.username, adminRequestDocument.password);
    await adminDashboardPage.accessManageRegularEnrollment();
    await regularEnrollmentPage.clickEnrollmentLearningTab();
    await regularEnrollmentPage.searchUsers(userSendDocument.firstname + ' ' + userSendDocument.lastname);
    await regularEnrollmentPage.viewEnrollmentDetailTLI();
    await expect(regularEnrollmentPage.verifyFullNameLocator).toBeVisible();
    await regularEnrollmentPage.requestDocument('ขาดเอกสารบัตรประชาชนยืนยันตน', selectCalendar);
    await homePage.logout();

    //User receive email
    const email = await testmailAppClient.fetchLatestEmailInbox(
      userSendDocument.email,
      '*ขอเอกสารเพิ่มเติมสำหรับหลักสูตร*',
      resetTimestamp,
    );
    expect(email.html).toContain('ขอเอกสารเพิ่มเติม');

    // User Login
    await loginPage.loginWithUsernameAndPassword(userSendDocument.username, userSendDocument.password);
    await attachmentPage.viewListDocument(userSendDocument.citizenId);
    await attachmentPage.uploadFileDocument(configuration.ekycs.uploadProfile);
    await attachmentPage.fillInformation();
    await attachmentPage.confirmInformation();
    await homePage.logoutOnDropdown();

    // Admin Login Verify Additional Document
    await loginPage.loginWithUsernameAndPassword(adminRequestDocument.username, adminRequestDocument.password);
    await adminDashboardPage.accessManageAdditionalDocuments();
    await additionalDocumentsPage.viewUserAdditionalDocumentDetail(
      userSendDocument.firstname + ' ' + userSendDocument.lastname,
    );
    await additionalDocumentsPage.approveDocument();
    await homePage.logout();
  });

  test('@SKL-T20022 Admin able to edit remark on enrollment detail', async ({
    configuration,
    loginPage,
    adminDashboardPage,
    trainingEnrollmentsPage,
    roundRepo,
    enrollmentsRepo,
    page,
  }) => {
    const learnerTLI = configuration.shareUsers.userSendDocument;
    const course = configuration.shareCourses.courseOICAdditionalDoc;
    const oicCourseVersions = configuration.courseVersions.oicCourse2Version1;
    const adminEditRemark = configuration.shareUsers.adminRequestDocument;

    const currentDate = new Date();
    const roundDate = currentDate;
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(23, 59, 0, 0);
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);

    // Seed round
    const round = await roundRepo.create({
      id: uuidv4(),
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: currentDate,
      updatedAt: currentDate,
      courseIds: oicCourseVersions.courseId,
      organizationId: learnerTLI.organizationId,
    });

    // Seed enrollment course OIC
    await enrollmentsRepo.create({
      id: uuidv4(),
      userId: learnerTLI.guid,
      status: EnrollmentsRepo.STATUS.inProgress,
      createdAt: currentDate,
      updatedAt: currentDate,
      startedAt: currentDate,
      expiredAt: expiredAt,
      completedCourseItem: 0,
      learningProgress: [],
      business: EnrollmentsRepo.BUSINESS.b2b,
      enrollType: EnrollType.voluntary,
      roundId: round.id,
      isCountdownArticle: false,
      isIdentityVerificationEnabled: true,
      organizationId: learnerTLI.organizationId,
      courseId: course.courseId,
      courseVersionId: oicCourseVersions.id,
      approvalAt: null,
      acceptedAt: null,
      externalContentType: '',
    });

    // Admin Login Request Additional Document OIC
    await loginPage.loginWithUsernameAndPassword(adminEditRemark.username, adminEditRemark.password);
    await adminDashboardPage.accessManageOICEnrollment();
    await trainingEnrollmentsPage.clickEnrollmentLearningTab();
    await trainingEnrollmentsPage.searchUsers(learnerTLI.firstname + ' ' + learnerTLI.lastname);
    await trainingEnrollmentsPage.viewEnrollmentDetailTLI();
    await expect(trainingEnrollmentsPage.verifyFullNameLocator).toBeVisible();
    // await page.pause();

    const remarkText = 'ทดสอบหมายเหตุ';
    await trainingEnrollmentsPage.editRemark(remarkText); // validate remark on function

    // Verify the remark result is displayed on screen
    await trainingEnrollmentsPage.verifyRemarkContent(remarkText);
    // await page.pause();
  });
  test('@SKL-T20023 Admin able to edit ID card image and face image on enrollment detail', async ({
    configuration,
    loginPage,
    homePage,
    myCoursePage,
    learningPage,
    adminDashboardPage,
    verifyIdentityPage,
    pendingApprovalPage,
    pendingApprovalDetailPage,
    enrollmentsRepo,
    roundRepo,
    logsFaceComparisonRepo,
    identificationCardsRepo,
    trainingEnrollmentsPage,
  }) => {
    const course = configuration.shareCourses.oicAutoApproveCourse;
    const userTLI = configuration.shareUsers.userTLIMatchCitizenCard;
    const organizationAdminTLI = configuration.usersLocal.organizationAdminTLI1;

    const date = new Date();
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const roundDate = new Date();
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    roundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(16, 59, 0, 0);

    const uniqueRoundId = uuidv4();
    const uniqueEnrollmentId = uuidv4();

    // Seed round
    const round = await roundRepo.create({
      id: uniqueRoundId,
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: date,
      updatedAt: date,
      courseIds: [course.courseId],
      organizationId: userTLI.organizationId,
    });

    // Enrollment OIC course to user
    const enrollment = await enrollmentsRepo.create({
      id: uniqueEnrollmentId,
      courseId: course.courseId,
      courseVersionId: course.courseVersions[1].id,
      organizationId: userTLI.organizationId,
      userId: userTLI.userId,
      business: EnrollmentsRepo.BUSINESS.b2c,
      status: EnrollmentsRepo.STATUS.inProgress,
      isCountdownArticle: false,
      roundId: round.id,
      completedCourseItem: 0,
      learningProgress: [],
      startedAt: date,
      expiredAt: expiredAt,
      acceptedAt: null,
      createdAt: date,
      updatedAt: date,
      isIdentityVerificationEnabled: true,
      remark: '',
      externalContentType: '',
      enrollType: EnrollType.voluntary,
      customerCode: '',
    });

    // User login
    await loginPage.loginWithUsernameAndPassword(userTLI.username, userTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();

    await myCoursePage.clickToMyCourse();
    await myCoursePage.waitForCourseLoaded();
    await myCoursePage.continueLearning(1);
    await verifyIdentityPage.submitAgreeConsent();
    await verifyIdentityPage.selectVerifyIDCardImage();
    await verifyIdentityPage.uploadIDCardImage(configuration.ekycs.validIdCardImagePath);
    await verifyIdentityPage.selectVerifyFaceImage();
    await verifyIdentityPage.confirmVerifyFaceImage();
    await verifyIdentityPage.startCourse();
    await homePage.logout();

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdminTLI.username, organizationAdminTLI.password);
    await adminDashboardPage.accessManageOICEnrollment();
    await trainingEnrollmentsPage.clickEnrollmentLearningTab();
    await trainingEnrollmentsPage.searchUsers(userTLI.firstname + ' ' + userTLI.lastname);
    await trainingEnrollmentsPage.viewEnrollmentDetailTLI();
    //delete id card image
    //confirm delete id card image
    //upload new id card image
    //delete face image
    //confirm delete face image
    //upload new face image
    //clear data
  });

  test('@SKL-T20024 Admin unable to upload ID card image and face image with invalid image on enrollment detail', async ({
    configuration,
    loginPage,
    homePage,
    myCoursePage,
    learningPage,
    adminDashboardPage,
    verifyIdentityPage,
    pendingApprovalPage,
    pendingApprovalDetailPage,
    enrollmentsRepo,
    roundRepo,
    logsFaceComparisonRepo,
    identificationCardsRepo,
    trainingEnrollmentsPage,
  }) => {
    const course = configuration.shareCourses.oicAutoApproveCourse;
    const userTLI = configuration.shareUsers.userTLIMatchCitizenCard;
    const organizationAdminTLI = configuration.usersLocal.organizationAdminTLI1;

    const date = new Date();
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);
    const roundDate = new Date();
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    roundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(16, 59, 0, 0);

    const uniqueRoundId = uuidv4();
    const uniqueEnrollmentId = uuidv4();

    // Seed round
    const round = await roundRepo.create({
      id: uniqueRoundId,
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: date,
      updatedAt: date,
      courseIds: [course.courseId],
      organizationId: userTLI.organizationId,
    });

    // Enrollment OIC course to user
    const enrollment = await enrollmentsRepo.create({
      id: uniqueEnrollmentId,
      courseId: course.courseId,
      courseVersionId: course.courseVersions[1].id,
      organizationId: userTLI.organizationId,
      userId: userTLI.userId,
      business: EnrollmentsRepo.BUSINESS.b2c,
      status: EnrollmentsRepo.STATUS.inProgress,
      isCountdownArticle: false,
      roundId: round.id,
      completedCourseItem: 0,
      learningProgress: [],
      startedAt: date,
      expiredAt: expiredAt,
      acceptedAt: null,
      createdAt: date,
      updatedAt: date,
      isIdentityVerificationEnabled: true,
      remark: '',
      externalContentType: '',
      enrollType: EnrollType.voluntary,
      customerCode: '',
    });

    // User login
    await loginPage.loginWithUsernameAndPassword(userTLI.username, userTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();

    await myCoursePage.clickToMyCourse();
    await myCoursePage.waitForCourseLoaded();
    await myCoursePage.continueLearning(1);
    await verifyIdentityPage.submitAgreeConsent();
    await verifyIdentityPage.selectVerifyIDCardImage();
    await verifyIdentityPage.uploadIDCardImage(configuration.ekycs.validIdCardImagePath);
    await verifyIdentityPage.selectVerifyFaceImage();
    await verifyIdentityPage.confirmVerifyFaceImage();
    await verifyIdentityPage.startCourse();
    await homePage.logout();

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdminTLI.username, organizationAdminTLI.password);
    await adminDashboardPage.accessManageOICEnrollment();
    await trainingEnrollmentsPage.clickEnrollmentLearningTab();
    await trainingEnrollmentsPage.searchUsers(userTLI.firstname + ' ' + userTLI.lastname);
    await trainingEnrollmentsPage.viewEnrollmentDetailTLI();
    //delete id card image
    //confirm delete id card image
    //upload new image (not id card) and verify
    //delete face image
    //confirm delete face image
    //upload new face image (2 people, not person)
    //clear data
  });

  test('SKL-T20035 Admin verify certificate when approve enrollment', async ({
    page,
    configuration,
    loginPage,
    homePage,
    myCoursePage,
    learningPage,
    adminDashboardPage,
    verifyIdentityPage,
    pendingApprovalPage,
    pendingApprovalDetailPage,
    enrollmentsRepo,
    roundRepo,
  }) => {
    const course = configuration.shareCourses.oicCourseManualApprove;
    const courseVersion = configuration.shareCourses.oicCourseManualApprove.courseVersions[1];
    const userTLI = configuration.shareUsers.userCommentTLI;
    const organizationAdminTLI = configuration.shareUsers.userAdminReplyTLI;

    const date = new Date();
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);

    const roundDate = new Date();
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    roundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(16, 59, 0, 0);

    // Seed round
    const round = await roundRepo.create({
      id: uuidv4(),
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: date,
      updatedAt: date,
      courseIds: [course.courseId],
      organizationId: userTLI.organizationId,
    });

    // Enrollment OIC course to user
    const enrollment = await enrollmentsRepo.create({
      id: uuidv4(),
      courseId: course.courseId,
      courseVersionId: courseVersion.id,
      organizationId: userTLI.organizationId,
      userId: userTLI.guid,
      business: EnrollmentsRepo.BUSINESS.b2c,
      status: EnrollmentsRepo.STATUS.inProgress,
      isCountdownArticle: false,
      roundId: round.id,
      completedCourseItem: 0,
      learningProgress: [],
      startedAt: date,
      expiredAt: expiredAt,
      acceptedAt: null,
      createdAt: date,
      updatedAt: date,
      isIdentityVerificationEnabled: false,
      remark: '',
      externalContentType: '',
      enrollType: EnrollType.voluntary,
      customerCode: '',
    });

    //User learn and submit course
    await loginPage.loginWithUsernameAndPassword(userTLI.username, userTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await myCoursePage.clickToMyCourse();
    await myCoursePage.waitForCourseLoaded();
    await myCoursePage.continueLearning(1);
    await verifyIdentityPage.submitAgreeConsent();
    await learningPage.finishOICCourseModalLocator.waitFor({ state: 'visible' });
    await learningPage.submitLearningResultOnModal();
    await myCoursePage.waitForCourseLoaded();
    await page.waitForLoadState('load');
    expect(await myCoursePage.verifyCourseStatusByCourseName(course.name)).toContain('รออนุมัติ');
    await homePage.logout();

    // Admin verify and approve enrollment
    await loginPage.loginWithUsernameAndPassword(organizationAdminTLI.username, organizationAdminTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessManagePendingApprovalOIC();
    await pendingApprovalPage.searchPendingApproval(`${userTLI.firstname} ${userTLI.lastname}`);
    await pendingApprovalDetailPage.accessPendingApprovalDetail();
    await pendingApprovalDetailPage.pendingApprovalElement.submitVerified();
    await expect(pendingApprovalDetailPage.successEditEnrollmentApprovalToastMsgLocator).toBeVisible();
    await pendingApprovalDetailPage.approvalHistoryTab.click();
    await expect(pendingApprovalDetailPage.latestApprovalHistoryStatusLocator).toContainText('ผ่านการตรวจสอบ');
    await expect(pendingApprovalDetailPage.latestApprovalHistoryReasonLocator).toContainText('-');
    await homePage.logout();

    //Learner verify pending approval status
    await loginPage.loginWithUsernameAndPassword(userTLI.username, userTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await myCoursePage.clickToMyCourse();
    await myCoursePage.waitForCourseLoaded();
    await page.waitForLoadState('load');
    expect(await myCoursePage.verifyCourseStatusByCourseName(course.name)).toContain('รออนุมัติ');
    await homePage.logout();

    //Admin approve enrollment
    await loginPage.loginWithUsernameAndPassword(organizationAdminTLI.username, organizationAdminTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await adminDashboardPage.accessManagePendingApprovalOIC();
    await pendingApprovalPage.searchPendingApproval(`${userTLI.firstname} ${userTLI.lastname}`);
    await pendingApprovalPage.filterStatus('ผ่านการตรวจสอบ');
    await pendingApprovalDetailPage.accessPendingApprovalDetail();
    await pendingApprovalDetailPage.pendingApprovalElement.submitApproval();
    await expect(pendingApprovalDetailPage.successEditEnrollmentApprovalToastMsgLocator).toBeVisible();
    await pendingApprovalDetailPage.approvalHistoryTab.click();
    await expect(pendingApprovalDetailPage.latestApprovalHistoryStatusLocator).toContainText('อนุมัติ');
    await expect(pendingApprovalDetailPage.latestApprovalHistoryReasonLocator).toContainText('-');
    await homePage.logout();

    //Learner verify approve status
    await loginPage.loginWithUsernameAndPassword(userTLI.username, userTLI.password);
    await expect(homePage.userProfileLocator).toBeVisible();
    await myCoursePage.clickToMyCourse();
    await myCoursePage.checkNoCourseFoundAndClickLearningHistory();
    await myCoursePage.verifyLearningHistoryStatus('การอบรมวิชาชีพประกัน (OIC)', courseVersion.name, 'อนุมัติ');
  });
});
